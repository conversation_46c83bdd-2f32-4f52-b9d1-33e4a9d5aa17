---
# 🚀 Ansible Playbook for Adding Permanent Routes
# 作者: jqa
# 功能: 在目标主机上添加永久路由配置

- name: 📡 添加永久路由配置
  hosts: qlwl
  become: yes
  gather_facts: yes
  strategy: free  # 🚀 允许主机并行执行

  vars:
    # 🔧 路由配置变量
    gateway_ip: "*************"
    network_interface: "bond1"
    backup_dir: "/home/<USER>/route_backup_{{ ansible_date_time.epoch }}"
    route_file_path: "route"
    # 🚀 性能优化选项
    enable_detailed_verification: false  # 设为true启用详细验证（较慢）
    batch_size: 50  # 批量处理大小

  tasks:
    # 📄 读取路由文件
    - name: 📄 读取路由配置文件
      slurp:
        src: "{{ route_file_path }}"
      register: route_file_content
      delegate_to: localhost
      run_once: true

    # 🔧 解析路由列表
    - name: 🔧 解析路由网络列表
      set_fact:
        route_networks: "{{ (route_file_content.content | b64decode).strip().split('\n') | select('match', '^[0-9]') | list }}"
      run_once: true

    # 📋 生成路由配置
    - name: 📋 生成完整路由配置
      set_fact:
        routes: "{{ routes | default([]) + [{'network': item, 'gateway': gateway_ip, 'device': network_interface}] }}"
      loop: "{{ route_networks }}"
      run_once: true

    # 🔍 系统信息收集
    - name: 📊 显示系统信息
      debug:
        msg: |
          🖥️  主机名: {{ ansible_hostname }}
          🐧 操作系统: {{ ansible_distribution }} {{ ansible_distribution_version }}
          🌐 网络接口: {{ ansible_interfaces }}
          📍 IP地址: {{ ansible_default_ipv4.address }}
          📋 路由数量: {{ routes | length }}

    # 🔧 检查网络接口
    - name: 🔍 检查网络接口是否存在
      shell: ip link show {{ network_interface }}
      register: interface_check
      failed_when: false
      changed_when: false

    - name: ❌ 网络接口不存在时退出
      fail:
        msg: "❌ 网络接口 {{ network_interface }} 不存在！请检查配置。"
      when: interface_check.rc != 0

    # 📁 创建备份目录
    - name: 📁 创建备份目录
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'

    # 💾 备份现有路由表
    - name: 💾 备份当前路由表
      shell: ip route show > {{ backup_dir }}/routes_backup.txt
      changed_when: false

    # 💾  检查是否存在永久路由文件
    - name: 检查是否存在永久路由文件
      stat:
        path: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
      register: file_check

    - name: 备份永久路由文件
      copy:
        src: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
        dest: "{{ backup_dir }}/route-{{ network_interface }}.bak"
        remote_src: yes
      when: file_check.stat.exists

    # 📝 为 CentOS/RHEL 系统批量添加永久路由配置
    - name: 📝 批量生成永久路由配置文件
      copy:
        content: |
          {% for route in routes %}
          {{ route.network }} via {{ route.gateway }} dev {{ route.device }}
          {% endfor %}
        dest: "/etc/sysconfig/network-scripts/route-{{ network_interface }}"
        backup: yes
        mode: '0644'

    # 🚀 批量生成路由添加脚本
    - name: 🚀 生成批量路由添加脚本
      copy:
        content: |
          #!/bin/bash
          # 🚀 批量添加路由脚本
          set +e  # 允许命令失败（路由可能已存在）

          SUCCESS_COUNT=0
          EXIST_COUNT=0
          FAIL_COUNT=0

          {% for route in routes %}
          if ip route add {{ route.network }} via {{ route.gateway }} dev {{ route.device }} 2>/dev/null; then
              echo "✅ 成功添加路由: {{ route.network }}"
              ((SUCCESS_COUNT++))
          else
              if ip route show | grep -q "{{ route.network }}"; then
                  echo "⚠️  路由已存在: {{ route.network }}"
                  ((EXIST_COUNT++))
              else
                  echo "❌ 添加失败: {{ route.network }}"
                  ((FAIL_COUNT++))
              fi
          fi
          {% endfor %}

          echo "📊 路由添加统计:"
          echo "  ✅ 成功添加: $SUCCESS_COUNT 条"
          echo "  ⚠️  已存在: $EXIST_COUNT 条"
          echo "  ❌ 添加失败: $FAIL_COUNT 条"
          echo "  📋 总计: {{ routes | length }} 条"
        dest: "{{ backup_dir }}/add_routes.sh"
        mode: '0755'

    # 🚀 执行批量路由添加
    - name: 🚀 批量添加路由到路由表
      shell: "{{ backup_dir }}/add_routes.sh"
      register: route_add_result
      changed_when: true

    # 📊 显示批量添加结果
    - name: 📊 显示路由添加结果
      debug:
        msg: |
          🎯 批量路由添加完成！
          {{ route_add_result.stdout }}

    # ✅ 快速验证路由（可选）
    - name: ✅ 快速验证路由表
      shell: |
        TOTAL_ROUTES={{ routes | length }}
        CURRENT_ROUTES=$(ip route show | grep -E "{{ routes | map(attribute='network') | join('|') }}" | wc -l)
        echo "📋 预期路由数量: $TOTAL_ROUTES"
        echo "📋 当前路由数量: $CURRENT_ROUTES"
        if [ $CURRENT_ROUTES -ge $((TOTAL_ROUTES * 80 / 100)) ]; then
            echo "✅ 路由验证通过 (≥80%)"
        else
            echo "⚠️  路由验证警告 (<80%)"
        fi
      register: route_quick_verify
      when: not enable_detailed_verification
      changed_when: false

    # 🔍 详细验证路由（可选，较慢）
    - name: 🔍 详细验证每条路由
      shell: ip route show | grep "{{ item.network }}"
      loop: "{{ routes }}"
      register: route_detailed_verify
      failed_when: false
      changed_when: false
      when: enable_detailed_verification

    # 📊 显示详细验证结果
    - name: 📊 显示详细验证统计
      debug:
        msg: |
          🔍 详细验证完成！
          ✅ 验证成功: {{ route_detailed_verify.results | selectattr('rc', 'equalto', 0) | list | length }} 条路由
          ❌ 验证失败: {{ route_detailed_verify.results | selectattr('rc', 'ne', 0) | list | length }} 条路由
      when: enable_detailed_verification

    # 💾 保存最终路由表
    - name: 💾 保存最终路由表
      shell: ip route show > {{ backup_dir }}/routes_final.txt
      changed_when: false

    # 📋 显示备份信息
    - name: 📋 显示备份信息
      debug:
        msg: |
          📁 备份目录: {{ backup_dir }}
          📄 备份文件:
            - routes_backup.txt (原始路由表)
            - routes_final.txt (最终路由表)