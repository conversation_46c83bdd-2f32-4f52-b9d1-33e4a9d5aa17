#!/bin/bash
###
# 🧪 测试脚本 - 演示调试模式功能
# 用法: ./test_debug_mode.sh
###

echo "🐘 === PostgreSQL分区表创建脚本 - 调试模式测试 ==="
echo

echo "1️⃣ 显示帮助信息:"
echo "----------------------------------------"
./creat_table_pg.sh --help
echo

echo "2️⃣ 调试模式运行 (只打印SQL，不执行):"
echo "----------------------------------------"
./creat_table_pg.sh --debug
echo

echo "3️⃣ 正常模式预览 (实际执行前可以先看看):"
echo "----------------------------------------"
echo "⚠️ 注意: 以下命令会实际执行SQL，请确保数据库连接正确"
echo "🚀 如需实际执行，请运行: ./creat_table_pg.sh"
echo

echo "✅ === 测试完成 ==="
echo "💡 建议使用流程:"
echo "   1. 🧪 首先运行调试模式验证SQL: ./creat_table_pg.sh --debug"
echo "   2. 🚀 确认无误后再正常执行: ./creat_table_pg.sh"
