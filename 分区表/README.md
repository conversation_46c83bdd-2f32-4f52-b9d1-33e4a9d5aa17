# 🐘 PostgreSQL分区表创建脚本优化说明

## 🚀 优化概述

已将原始的 `creat_table_pg.sh` 脚本进行了全面重构和优化，主要改进包括：

## ✨ 主要优化点

### 1. 📋 配置管理
- **内嵌配置**：所有配置项都集中在脚本顶部的配置区域，无需单独的配置文件
- **只读变量**：使用 `readonly` 声明配置变量，防止意外修改
- **清晰分组**：配置按功能分组（基础配置、数据库配置、表配置等）

### 2. 🛡️ 错误处理和日志
- **严格模式**：使用 `set -euo pipefail` 提高脚本健壮性
- **统一日志格式**：带时间戳的结构化日志
- **多级日志**：INFO、ERROR、WARN、DEBUG 四个级别
- **彩色输出**：终端输出带颜色和emoji，提高可读性
- **详细错误信息**：包含行号和具体命令的错误报告

### 3. 🏗️ 代码结构
- **函数化编程**：将功能拆分为独立的函数
- **代码复用**：消除重复代码，提高维护性
- **清晰命名**：使用描述性的函数和变量名
- **模块化设计**：每个函数职责单一，便于测试和维护

### 4. 🚀 功能增强
- **环境验证**：启动前检查必要的命令和权限
- **SQL预览**：执行前显示将要创建的SQL语句
- **脚本信息**：显示详细的运行环境信息
- **优雅退出**：正确处理用户中断信号
- **调试模式**：只打印SQL不执行，安全测试

## 配置说明

### 数据库配置
```bash
# 数据库组1
readonly DB1_HOST="************"
readonly DB1_PORT="18921"
readonly DB1_USER="uccp_5gc"
readonly DB1_PASS="a4#FiGqH"
readonly DB1_DATABASE="kong_production"

# 数据库组2
readonly DB2_HOST="*************"
readonly DB2_PORT="18921"
readonly DB2_USER="ckpg_app"
readonly DB2_PASS="Tr1#d5n%65e8"
readonly -a DB2_DATABASES=("kong_production" "kong_production2" "kong_gzq" "kong_scc")
```

### 表配置
```bash
readonly -a PARTITION_TABLES=("api_instances" "api_instance_histories")
```

### 运行模式配置
```bash
DEBUG_MODE=false  # 调试模式：true=只打印SQL不执行，false=正常执行
```

### 路径配置
```bash
readonly LOG_DIR="/root/scripts/backup/shell/log"
readonly MONITOR_SCRIPT="/root/scripts/backup/shell/monitor"
```

## 使用方法

### 基本使用
```bash
# 正常执行模式
./creat_table_pg.sh

# 调试模式（只打印SQL，不实际执行）
./creat_table_pg.sh --debug
./creat_table_pg.sh -d

# 显示帮助信息
./creat_table_pg.sh --help
./creat_table_pg.sh -h
```

### 调试模式特性
- **安全测试**：只打印SQL语句，不实际连接数据库
- **语法验证**：验证生成的SQL语句格式是否正确
- **配置检查**：确认数据库连接参数和表配置
- **彩色输出**：使用青色高亮显示调试信息

### 日志查看
```bash
# 查看信息日志
tail -f /root/scripts/backup/shell/log/$(date +%F)-info.log

# 查看错误日志
tail -f /root/scripts/backup/shell/log/$(date +%F)-err.log
```

## 脚本执行流程

1. **环境验证**
   - 检查 psql 命令是否可用
   - 验证日志目录权限
   - 检查监控脚本状态

2. **信息展示**
   - 显示脚本配置信息
   - 预览即将执行的SQL语句

3. **分区表创建**
   - 处理数据库组1（************）
   - 处理数据库组2（*************）
   - 为每个数据库创建下个月的分区表

4. **结果报告**
   - 记录执行结果
   - 发送成功/失败告警

## 安全特性

- **密码保护**：使用环境变量传递密码，避免命令行泄露
- **权限检查**：验证必要的文件和目录权限
- **错误隔离**：单个数据库失败不影响其他数据库的处理
- **信号处理**：正确处理用户中断和系统信号

## 自定义配置

如需修改配置，只需编辑脚本顶部的配置区域：

1. **添加新数据库**：在 `DB2_DATABASES` 数组中添加
2. **修改表名**：在 `PARTITION_TABLES` 数组中修改
3. **更改路径**：修改 `LOG_DIR` 和 `MONITOR_SCRIPT` 变量
4. **调整连接参数**：修改对应的数据库配置变量

## 故障排除

### 常见问题
1. **psql命令未找到**：安装PostgreSQL客户端
2. **权限不足**：检查日志目录写权限
3. **连接失败**：验证数据库连接参数
4. **SQL执行失败**：检查表结构和分区配置

### 调试模式
脚本包含详细的DEBUG日志，可以通过查看日志文件进行问题诊断。

## 调试模式使用场景

### 1. 首次部署验证
```bash
# 在新环境中首次运行前，先用调试模式验证配置
./creat_table_pg.sh --debug
```

### 2. 配置变更测试
```bash
# 修改配置后，验证生成的SQL是否正确
./creat_table_pg.sh -d
```

### 3. 故障排查
```bash
# 当正常执行出现问题时，用调试模式检查SQL语句
./creat_table_pg.sh --debug > debug_output.log 2>&1
```

## 版本历史

- **v2.1** (2025-07-14): 增加调试模式，支持命令行参数
- **v2.0** (2025-07-14): 完全重构，内嵌配置，增强错误处理
- **v1.0** (2023-12-28): 原始版本
