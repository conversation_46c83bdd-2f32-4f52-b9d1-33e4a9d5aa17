#!/bin/bash
###
# <AUTHOR> lml
# @Date                : 2023-05-07 15:18:25
# @LastEditors         : lml
# @LastEditTime        : 2025-07-14 15:24:57
# @FilePath            : creat_table_pg.sh
# @Description         : 用于创建PostgreSQL分区表的优化脚本
###

# 设置严格模式（移除-e选项，允许命令失败后继续执行）
set -uo pipefail

# ==================== 配置区域 ====================
# 脚本基础配置
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_DIR="/root/scripts/backup/shell/log"
readonly DATE_TODAY=$(date +%F)
readonly ERROR_LOG="${LOG_DIR}/${DATE_TODAY}-err.log"
readonly INFO_LOG="${LOG_DIR}/${DATE_TODAY}-info.log"
readonly MONITOR_SCRIPT="/root/scripts/backup/shell/monitor"

# 数据库配置 - 组1 (************)
readonly DB1_HOST="************"
readonly DB1_PORT="18921"
readonly DB1_USER="uccp_5gc"
readonly DB1_PASS="a4#FiGqH"
readonly DB1_DATABASE="kong_production"

# 数据库配置 - 组2 (*************)
readonly DB2_HOST="*************"
readonly DB2_PORT="18921"
readonly DB2_USER="ckpg_app"
readonly DB2_PASS="Tr1#d5n%65e8"
readonly -a DB2_DATABASES=("kong_production" "kong_production2" "kong_gzq" "kong_scc")

# 需要创建分区的表
readonly -a PARTITION_TABLES=("api_instances" "api_instance_histories")

# 运行模式配置
DEBUG_MODE=false  # 调试模式：true=只打印SQL不执行，false=正常执行

# 错误统计
declare -g ERROR_COUNT=0
declare -g TOTAL_OPERATIONS=0
declare -g ERROR_DETAILS=""  # 存储详细错误信息

# 颜色配置（用于终端输出）
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color
# ==================== 配置区域结束 ====================

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--debug)
                DEBUG_MODE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
🐘 PostgreSQL分区表创建脚本

📖 用法: $0 [选项]

🔧 选项:
    -d, --debug     🧪 调试模式：只打印SQL语句，不实际执行
    -h, --help      ❓ 显示此帮助信息

💡 示例:
    $0              🚀 正常执行模式
    $0 --debug      🧪 调试模式，只打印SQL
    $0 -d           🧪 调试模式简写

📝 说明:
    • 调试模式安全无害，可用于验证SQL语句
    • 正常模式会实际连接数据库并执行SQL
    • 建议先用调试模式验证，再正常执行

EOF
}

# 创建日志目录
mkdir -p "${LOG_DIR}"

# 信号处理
trap 'handle_exit' SIGINT SIGTERM
trap 'handle_error ${LINENO} "${BASH_COMMAND}"' ERR

# 日志函数
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[${timestamp}] [INFO] ✅${NC} ${message}" | tee -a "${INFO_LOG}"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[${timestamp}] [ERROR] ❌${NC} ${message}" | tee -a "${ERROR_LOG}" >&2
}

log_warn() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[${timestamp}] [WARN] ⚠️${NC} ${message}" | tee -a "${INFO_LOG}"
}

log_debug() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[${timestamp}] [DEBUG] 🔍${NC} ${message}" | tee -a "${INFO_LOG}"
}

# 错误处理函数
handle_error() {
    local line_number="$1"
    local command="$2"
    local error_msg="💥 脚本异常: 第${line_number}行执行命令失败: ${command}"

    log_error "${error_msg}"
    send_alert "分区表创建失败" "${error_msg}"
    exit 1
}

handle_exit() {
    log_info "👋 用户中断脚本执行"
    exit 0
}

# 发送告警
send_alert() {
    local subject="$1"
    local message="$2"

    if [[ -x "${MONITOR_SCRIPT}" ]]; then
        cd "${SCRIPT_DIR}"
        "${MONITOR_SCRIPT}" postumc -p "${subject}" -a "${message}" || {
            log_warn "📢 告警发送失败，但继续执行"
        }
    else
        log_warn "📢 监控脚本不存在或不可执行: ${MONITOR_SCRIPT}"
    fi
}

# 生成下个月的分区表SQL
generate_partition_sql() {
    local table_name="$1"
    local next_month_start next_month_end partition_name

    # 计算下个月的开始和结束日期
    next_month_start=$(date -d '+1 month' '+%Y-%m-01')
    next_month_end=$(date -d '+2 month' '+%Y-%m-01')
    partition_name="${table_name}_$(date -d '+1 month' '+%Y%m')"

    echo "CREATE TABLE ${partition_name} PARTITION OF ${table_name} FOR VALUES FROM ('${next_month_start}') TO ('${next_month_end}');"
}

# 执行SQL命令
execute_sql() {
    local host="$1"
    local port="$2"
    local username="$3"
    local password="$4"
    local database="$5"
    local sql="$6"
    local connection_info="${host}:${port}/${database}"

    if [[ "${DEBUG_MODE}" == "true" ]]; then
        # 调试模式：只打印SQL，不实际执行
        echo -e "${CYAN}[DEBUG] 🔗 连接: ${connection_info}${NC}"
        echo -e "${CYAN}[DEBUG] 📝 SQL: ${sql}${NC}"
        log_info "🧪 [调试模式] 模拟执行SQL - 连接: ${connection_info}"
        log_info "📝 [调试模式] SQL: ${sql}"
        return 0
    else
        # 正常模式：实际执行SQL
        log_debug "🚀 开始执行SQL - 连接: ${connection_info}"
        log_debug "📝 SQL: ${sql}"

        # 使用临时文件捕获psql的输出和错误
        local temp_output=$(mktemp)
        local temp_error=$(mktemp)

        if PGPASSWORD="${password}" psql -h "${host}" -p "${port}" -U "${username}" -d "${database}" -c "${sql}" > "${temp_output}" 2> "${temp_error}"; then
            # 成功：将输出写入info日志
            cat "${temp_output}" >> "${INFO_LOG}"
            log_info "🎉 SQL执行成功 - 连接: ${connection_info}"
            rm -f "${temp_output}" "${temp_error}"
            return 0
        else
            # 失败：将错误信息同时写入info和error日志
            local error_content=$(cat "${temp_error}")
            cat "${temp_error}" >> "${INFO_LOG}"
            cat "${temp_error}" >> "${ERROR_LOG}"
            log_error "💥 SQL执行失败 - 连接: ${connection_info}"
            log_error "❌ 失败的SQL: ${sql}"

            # 将错误信息存储到全局变量中，供告警使用
            if [[ -n "${error_content}" ]]; then
                if [[ -n "${ERROR_DETAILS}" ]]; then
                    ERROR_DETAILS="${ERROR_DETAILS}\n• ${connection_info}: ${error_content}"
                else
                    ERROR_DETAILS="• ${connection_info}: ${error_content}"
                fi
            fi

            rm -f "${temp_output}" "${temp_error}"
            return 1
        fi
    fi
}

# 为单个数据库创建分区表
create_partitions_for_database() {
    local host="$1"
    local port="$2"
    local username="$3"
    local password="$4"
    local database="$5"
    shift 5
    local tables=("$@")

    local connection_info="${host}:${port}/${database}"
    local local_error_count=0
    log_info "🏗️ 开始为数据库创建分区表: ${connection_info}"

    for table in "${tables[@]}"; do
        local sql=$(generate_partition_sql "${table}")
        ((TOTAL_OPERATIONS++))
        if ! execute_sql "${host}" "${port}" "${username}" "${password}" "${database}" "${sql}"; then
            log_error "💥 为表 ${table} 创建分区失败"
            # 发送包含具体错误信息的告警
            local alert_msg="数据库: ${connection_info}, 表: ${table}"
            if [[ -n "${ERROR_DETAILS}" ]]; then
                # 获取最新的错误信息（最后一行）
                local latest_error=$(echo -e "${ERROR_DETAILS}" | tail -1)
                alert_msg="${alert_msg}\n错误详情: ${latest_error}"
            fi
            send_alert "分区表创建失败" "${alert_msg}"
            ((ERROR_COUNT++))
            ((local_error_count++))
            log_warn "⚠️ 继续处理下一个表..."
        else
            log_info "✅ 表 ${table} 分区创建成功"
        fi
    done

    if [[ ${local_error_count} -eq 0 ]]; then
        log_info "🎯 数据库 ${connection_info} 所有分区表创建完成"
        return 0
    else
        log_warn "⚠️ 数据库 ${connection_info} 有 ${local_error_count} 个表分区创建失败，但已处理完所有表"
        return 1
    fi
}

# 处理第一组数据库 (************)
process_database_group_1() {
    log_info "🏢 开始处理数据库组1: ${DB1_HOST}"
    local group_success=true

    if ! create_partitions_for_database "${DB1_HOST}" "${DB1_PORT}" "${DB1_USER}" "${DB1_PASS}" "${DB1_DATABASE}" "${PARTITION_TABLES[@]}"; then
        log_warn "⚠️ 数据库组1处理过程中有错误，但已完成所有操作"
        group_success=false
    fi

    if [[ "${group_success}" == "true" ]]; then
        log_info "🎉 数据库组1处理完成"
        return 0
    else
        log_warn "⚠️ 数据库组1处理完成，但有部分错误"
        return 1
    fi
}

# 处理第二组数据库 (*************)
process_database_group_2() {
    log_info "🏢 开始处理数据库组2: ${DB2_HOST}"
    local group_error_count=0

    for database in "${DB2_DATABASES[@]}"; do
        if ! create_partitions_for_database "${DB2_HOST}" "${DB2_PORT}" "${DB2_USER}" "${DB2_PASS}" "${database}" "${PARTITION_TABLES[@]}"; then
            log_warn "⚠️ 数据库组2中数据库 ${database} 处理过程中有错误"
            ((group_error_count++))
        else
            log_info "✅ 数据库 ${database} 处理完成"
        fi
        log_info "📊 继续处理下一个数据库..."
    done

    if [[ ${group_error_count} -eq 0 ]]; then
        log_info "🎉 数据库组2所有数据库处理完成"
        return 0
    else
        log_warn "⚠️ 数据库组2处理完成，但有 ${group_error_count} 个数据库存在错误"
        return 1
    fi
}

# 验证环境
validate_environment() {
    log_info "🔍 开始环境验证..."

    if [[ "${DEBUG_MODE}" == "true" ]]; then
        log_info "🧪 调试模式：跳过环境验证"
        return 0
    fi

    # 检查psql命令是否可用
    if ! command -v psql &> /dev/null; then
        log_error "🚫 psql命令未找到，请确保PostgreSQL客户端已安装"
        exit 1
    fi

    # 检查日志目录权限
    if [[ ! -w "${LOG_DIR}" ]]; then
        log_error "🚫 日志目录无写权限: ${LOG_DIR}"
        exit 1
    fi

    # 检查监控脚本（非必需）
    if [[ ! -x "${MONITOR_SCRIPT}" ]]; then
        log_warn "⚠️ 监控脚本不存在或不可执行: ${MONITOR_SCRIPT}"
        log_warn "⚠️ 将跳过告警发送功能"
    fi

    log_info "✅ 环境验证通过"
}

# 显示脚本信息
show_script_info() {
    log_info "🐘 === PostgreSQL分区表创建脚本 ==="

    if [[ "${DEBUG_MODE}" == "true" ]]; then
        echo -e "${YELLOW}🧪 === 调试模式已启用 ===${NC}"
        log_info "🧪 运行模式: 调试模式 (只打印SQL，不实际执行)"
    else
        log_info "🚀 运行模式: 正常执行模式"
    fi

    log_info "📁 脚本路径: ${SCRIPT_DIR}"
    log_info "📂 日志目录: ${LOG_DIR}"
    log_info "📄 错误日志: ${ERROR_LOG}"
    log_info "📄 信息日志: ${INFO_LOG}"
    log_info "📊 目标表: ${PARTITION_TABLES[*]}"
    log_info "🏢 数据库组1: ${DB1_HOST}:${DB1_PORT}/${DB1_DATABASE}"
    log_info "🏢 数据库组2: ${DB2_HOST}:${DB2_PORT} (${#DB2_DATABASES[@]}个数据库)"
}

# 预览即将执行的SQL
preview_sql() {
    if [[ "${DEBUG_MODE}" == "true" ]]; then
        echo -e "${CYAN}📝 === 调试模式 - SQL语句预览 ===${NC}"
    else
        log_info "📝 === 即将创建的分区表SQL预览 ==="
    fi

    for table in "${PARTITION_TABLES[@]}"; do
        local sql=$(generate_partition_sql "${table}")
        if [[ "${DEBUG_MODE}" == "true" ]]; then
            echo -e "${CYAN}📊 表 ${table}:${NC} ${sql}"
        else
            log_info "📊 表 ${table}: ${sql}"
        fi
    done
}

# 主函数
main() {
    # 解析命令行参数
    parse_arguments "$@"

    show_script_info
    validate_environment
    preview_sql

    if [[ "${DEBUG_MODE}" == "true" ]]; then
        echo -e "${CYAN}🧪 === 调试模式 - 开始模拟执行 ===${NC}"
        log_info "🧪 === 调试模式 - 开始模拟分区表创建 ==="
    else
        log_info "🚀 === 开始执行分区表创建 ==="
    fi

    # 执行数据库操作
    local group1_success=true
    local group2_success=true

    # 处理数据库组1
    if ! process_database_group_1; then
        log_warn "⚠️ 数据库组1处理过程中有错误，但继续处理下一组"
        group1_success=false
    fi

    # 处理数据库组2
    if ! process_database_group_2; then
        log_warn "⚠️ 数据库组2处理过程中有错误"
        group2_success=false
    fi

    # 生成最终报告
    log_info "📊 === 执行结果统计 ==="
    log_info "📈 总操作数: ${TOTAL_OPERATIONS}"
    log_info "❌ 失败操作数: ${ERROR_COUNT}"
    log_info "✅ 成功操作数: $((TOTAL_OPERATIONS - ERROR_COUNT))"

    if [[ "${DEBUG_MODE}" == "true" ]]; then
        echo -e "${GREEN}🎉 === 调试模式完成 - 所有SQL语句已打印 ===${NC}"
        log_info "🎉 === 调试模式完成 - 所有SQL语句已打印 ==="
    elif [[ ${ERROR_COUNT} -eq 0 ]]; then
        log_info "🎉 === 所有分区表创建成功 ==="
        send_alert "分区表创建成功" "所有数据库的分区表已成功创建"
    elif [[ ${ERROR_COUNT} -lt ${TOTAL_OPERATIONS} ]]; then
        log_warn "⚠️ === 分区表创建部分成功 ==="
        log_warn "⚠️ 成功: $((TOTAL_OPERATIONS - ERROR_COUNT))/${TOTAL_OPERATIONS} 个操作"
        local summary_msg="成功: $((TOTAL_OPERATIONS - ERROR_COUNT))/${TOTAL_OPERATIONS} 个操作，失败: ${ERROR_COUNT} 个操作"
        if [[ -n "${ERROR_DETAILS}" ]]; then
            summary_msg="${summary_msg}\n\n失败详情:\n${ERROR_DETAILS}"
        fi
        send_alert "分区表创建部分成功" "${summary_msg}"
    else
        log_error "💥 === 所有分区表创建失败 ==="
        local failure_msg="所有 ${TOTAL_OPERATIONS} 个操作都失败了"
        if [[ -n "${ERROR_DETAILS}" ]]; then
            failure_msg="${failure_msg}\n\n失败详情:\n${ERROR_DETAILS}"
        fi
        send_alert "分区表创建失败" "${failure_msg}"
        exit 1
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi